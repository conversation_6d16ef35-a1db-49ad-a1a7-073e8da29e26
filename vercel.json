{"version": 2, "builds": [{"src": "docs/**/*", "use": "@vercel/static"}], "rewrites": [{"source": "/", "destination": "/docs/index.html"}, {"source": "/index.html", "destination": "/docs/index.html"}, {"source": "/llm-docs.html", "destination": "/docs/llm-docs.html"}, {"source": "/technical-docs.html", "destination": "/docs/technical-docs.html"}, {"source": "/styles/(.*)", "destination": "/docs/styles/$1"}, {"source": "/scripts/(.*)", "destination": "/docs/scripts/$1"}, {"source": "/(.*)", "destination": "/docs/$1"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/styles/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/scripts/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "cleanUrls": true, "trailingSlash": false}